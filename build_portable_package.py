#!/usr/bin/env python3
"""
公司制度查询平台 - Portable Python环境构建脚本
实现零配置交付包，确保AI问答功能在用户环境中正常运行
"""
import os
import sys
import shutil
import subprocess
import urllib.request
import zipfile
import tempfile
from pathlib import Path
import json
import time

class PortablePackageBuilder:
    """便携包构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_portable"
        self.package_dir = self.build_dir / "CompanyPolicySystem"
        self.python_dir = self.package_dir / "python"
        self.venv_dir = self.package_dir / "venv"
        
        # Python嵌入式版本配置
        self.python_version = "3.10.11"
        self.python_url = f"https://www.python.org/ftp/python/{self.python_version}/python-{self.python_version}-embed-amd64.zip"
        
        print(f"项目根目录: {self.project_root}")
        print(f"构建目录: {self.build_dir}")
        print(f"包目录: {self.package_dir}")
    
    def clean_build_dir(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        try:
            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)
            self.build_dir.mkdir(parents=True, exist_ok=True)
            print("✅ 构建目录清理完成")
            return True
        except Exception as e:
            print(f"❌ 清理构建目录失败: {e}")
            return False
    
    def download_python_embedded(self):
        """下载Python嵌入式版本"""
        print(f"📥 下载Python {self.python_version} 嵌入式版本...")
        
        python_zip = self.build_dir / f"python-{self.python_version}-embed-amd64.zip"
        
        if python_zip.exists():
            print("Python嵌入式版本已存在，跳过下载")
        else:
            try:
                print(f"从 {self.python_url} 下载...")
                urllib.request.urlretrieve(self.python_url, python_zip)
                print("✅ Python嵌入式版本下载完成")
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                print("💡 请手动下载Python嵌入式版本并放置在build_portable目录")
                return False
        
        # 解压Python
        print("📦 解压Python嵌入式版本...")
        self.python_dir.mkdir(parents=True, exist_ok=True)
        
        with zipfile.ZipFile(python_zip, 'r') as zip_ref:
            zip_ref.extractall(self.python_dir)
        
        print("✅ Python嵌入式版本解压完成")
        return True
    
    def configure_python_embedded(self):
        """配置Python嵌入式版本"""
        print("⚙️ 配置Python嵌入式版本...")
        
        # 修改python310._pth文件以启用site-packages
        pth_file = self.python_dir / f"python{self.python_version.replace('.', '')[:3]}._pth"
        if pth_file.exists():
            content = pth_file.read_text()
            if "#import site" in content:
                content = content.replace("#import site", "import site")
                pth_file.write_text(content)
                print("✅ 启用site-packages支持")
        
        # 下载get-pip.py
        get_pip_path = self.python_dir / "get-pip.py"
        if not get_pip_path.exists():
            print("📥 下载get-pip.py...")
            try:
                urllib.request.urlretrieve(
                    "https://bootstrap.pypa.io/get-pip.py", 
                    get_pip_path
                )
                print("✅ get-pip.py下载完成")
            except Exception as e:
                print(f"❌ get-pip.py下载失败: {e}")
                return False
        
        # 安装pip
        print("📦 安装pip...")
        python_exe = self.python_dir / "python.exe"
        try:
            subprocess.run([
                str(python_exe), 
                str(get_pip_path),
                "--no-warn-script-location"
            ], check=True, cwd=str(self.python_dir))
            print("✅ pip安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ pip安装失败: {e}")
            return False
        
        return True
    
    def create_virtual_environment(self):
        """创建虚拟环境"""
        print("🐍 创建虚拟环境...")
        
        python_exe = self.python_dir / "python.exe"
        
        try:
            # 创建虚拟环境
            subprocess.run([
                str(python_exe), "-m", "venv", 
                str(self.venv_dir),
                "--system-site-packages"
            ], check=True)
            print("✅ 虚拟环境创建完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 虚拟环境创建失败: {e}")
            return False
        
        return True
    
    def install_dependencies(self):
        """安装项目依赖"""
        print("📦 安装项目依赖...")
        
        venv_python = self.venv_dir / "Scripts" / "python.exe"
        venv_pip = self.venv_dir / "Scripts" / "pip.exe"
        
        requirements_file = self.project_root / "requirements.txt"
        
        if not requirements_file.exists():
            print("❌ requirements.txt文件不存在")
            return False
        
        try:
            # 升级pip
            subprocess.run([
                str(venv_pip), "install", "--upgrade", "pip"
            ], check=True)
            
            # 安装依赖
            print("正在安装依赖包...")
            subprocess.run([
                str(venv_pip), "install", "-r", str(requirements_file),
                "--no-warn-script-location"
            ], check=True)
            
            print("✅ 项目依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
        
        return True
    
    def copy_project_files(self):
        """复制项目文件"""
        print("📁 复制项目文件...")
        
        # 需要复制的文件和目录
        items_to_copy = [
            "main.py",
            "config.yaml", 
            "src/",
            "static/",
            "data/",
            "models/",
            "docs/",
            "README.md",
            "LICENSE.txt"
        ]
        
        for item in items_to_copy:
            src_path = self.project_root / item
            dst_path = self.package_dir / item
            
            if src_path.exists():
                if src_path.is_file():
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src_path, dst_path)
                    print(f"✅ 复制文件: {item}")
                elif src_path.is_dir():
                    if dst_path.exists():
                        shutil.rmtree(dst_path)
                    shutil.copytree(src_path, dst_path)
                    print(f"✅ 复制目录: {item}")
            else:
                print(f"⚠️ 文件/目录不存在: {item}")
        
        print("✅ 项目文件复制完成")
        return True
    
    def build(self):
        """构建便携包"""
        print("🚀 开始构建便携包...")
        start_time = time.time()
        
        steps = [
            ("清理构建目录", self.clean_build_dir),
            ("下载Python嵌入式版本", self.download_python_embedded),
            ("配置Python嵌入式版本", self.configure_python_embedded),
            ("创建虚拟环境", self.create_virtual_environment),
            ("安装项目依赖", self.install_dependencies),
            ("复制项目文件", self.copy_project_files),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 执行步骤: {step_name}")
            if not step_func():
                print(f"❌ 步骤失败: {step_name}")
                return False
        
        elapsed_time = time.time() - start_time
        print(f"\n🎉 便携包构建完成！")
        print(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
        print(f"📦 包位置: {self.package_dir}")
        
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🏢 公司制度查询平台 - Portable Python环境构建")
    print("=" * 60)
    
    builder = PortablePackageBuilder()
    
    if builder.build():
        print("\n✅ 构建成功！")
        print("💡 下一步: 运行配置脚本来设置向量数据库路径")
    else:
        print("\n❌ 构建失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
