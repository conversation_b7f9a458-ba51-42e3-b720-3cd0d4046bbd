#!/usr/bin/env python3
"""
创建增量更新系统
包含更新包生成器和用户更新工具
"""
import os
import sys
import json
import zipfile
import hashlib
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

def create_update_generator():
    """创建更新包生成器"""
    project_root = Path(__file__).parent
    build_dir = project_root / "build_portable"
    update_tools_dir = build_dir / "update_tools"
    
    print("🔄 创建更新包生成器...")
    
    update_tools_dir.mkdir(parents=True, exist_ok=True)
    
    # 更新包生成器脚本
    generator_script = '''#!/usr/bin/env python3
"""
更新包生成器 - 管理员工具
用于生成文档增量更新包
"""
import os
import sys
import json
import zipfile
import hashlib
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class UpdatePackageGenerator:
    """更新包生成器"""
    
    def __init__(self, base_path: str = None):
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.docs_dir = self.base_path / "docs"
        self.data_dir = self.base_path / "data"
        self.output_dir = self.base_path / "updates"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"📁 基础路径: {self.base_path}")
        print(f"📄 文档目录: {self.docs_dir}")
        print(f"💾 数据目录: {self.data_dir}")
        print(f"📦 输出目录: {self.output_dir}")
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def scan_documents(self) -> Dict[str, Any]:
        """扫描文档目录，生成文件清单"""
        print("🔍 扫描文档目录...")
        
        file_manifest = {}
        
        if not self.docs_dir.exists():
            print("❌ 文档目录不存在")
            return file_manifest
        
        for file_path in self.docs_dir.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(self.docs_dir)
                file_hash = self.calculate_file_hash(file_path)
                file_size = file_path.stat().st_size
                modified_time = file_path.stat().st_mtime
                
                file_manifest[str(relative_path)] = {
                    "hash": file_hash,
                    "size": file_size,
                    "modified": modified_time,
                    "full_path": str(file_path)
                }
        
        print(f"✅ 扫描完成，发现 {len(file_manifest)} 个文件")
        return file_manifest
    
    def load_previous_manifest(self) -> Dict[str, Any]:
        """加载上次的文件清单"""
        manifest_file = self.output_dir / "last_manifest.json"
        
        if manifest_file.exists():
            try:
                with open(manifest_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载上次清单失败: {e}")
        
        return {}
    
    def save_manifest(self, manifest: Dict[str, Any]):
        """保存文件清单"""
        manifest_file = self.output_dir / "last_manifest.json"
        
        with open(manifest_file, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 文件清单已保存: {manifest_file}")
    
    def find_changes(self, current_manifest: Dict[str, Any], 
                    previous_manifest: Dict[str, Any]) -> Dict[str, List[str]]:
        """查找文件变更"""
        print("🔍 分析文件变更...")
        
        changes = {
            "added": [],      # 新增文件
            "modified": [],   # 修改文件
            "deleted": []     # 删除文件
        }
        
        # 查找新增和修改的文件
        for file_path, file_info in current_manifest.items():
            if file_path not in previous_manifest:
                changes["added"].append(file_path)
            elif file_info["hash"] != previous_manifest[file_path]["hash"]:
                changes["modified"].append(file_path)
        
        # 查找删除的文件
        for file_path in previous_manifest:
            if file_path not in current_manifest:
                changes["deleted"].append(file_path)
        
        print(f"📊 变更统计:")
        print(f"   新增: {len(changes['added'])} 个文件")
        print(f"   修改: {len(changes['modified'])} 个文件")
        print(f"   删除: {len(changes['deleted'])} 个文件")
        
        return changes
    
    def create_update_package(self, changes: Dict[str, List[str]], 
                            current_manifest: Dict[str, Any]) -> str:
        """创建更新包"""
        if not any(changes.values()):
            print("ℹ️ 没有文件变更，无需创建更新包")
            return None
        
        # 生成更新包文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        update_filename = f"update_{timestamp}.zip"
        update_path = self.output_dir / update_filename
        
        print(f"📦 创建更新包: {update_filename}")
        
        # 创建更新包
        with zipfile.ZipFile(update_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加变更信息
            update_info = {
                "version": timestamp,
                "created": datetime.now().isoformat(),
                "changes": changes,
                "total_files": len(changes["added"]) + len(changes["modified"])
            }
            
            zipf.writestr("update_info.json", 
                         json.dumps(update_info, indent=2, ensure_ascii=False))
            
            # 添加新增和修改的文件
            files_to_include = changes["added"] + changes["modified"]
            
            for file_path in files_to_include:
                if file_path in current_manifest:
                    full_path = current_manifest[file_path]["full_path"]
                    if os.path.exists(full_path):
                        # 在ZIP中保持相对路径结构
                        zipf.write(full_path, f"docs/{file_path}")
                        print(f"   ✅ 添加文件: {file_path}")
            
            # 如果有向量数据库文件，也包含进去
            vector_db_dir = self.data_dir / "chroma_db"
            if vector_db_dir.exists():
                for db_file in vector_db_dir.rglob("*"):
                    if db_file.is_file():
                        rel_path = db_file.relative_to(self.data_dir)
                        zipf.write(db_file, f"data/{rel_path}")
        
        print(f"✅ 更新包创建完成: {update_path}")
        print(f"📊 包大小: {update_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        return str(update_path)
    
    def generate_update(self) -> str:
        """生成更新包"""
        print("🚀 开始生成更新包...")
        
        # 扫描当前文档
        current_manifest = self.scan_documents()
        
        # 加载上次清单
        previous_manifest = self.load_previous_manifest()
        
        # 查找变更
        changes = self.find_changes(current_manifest, previous_manifest)
        
        # 创建更新包
        update_package = self.create_update_package(changes, current_manifest)
        
        # 保存当前清单
        self.save_manifest(current_manifest)
        
        return update_package

def main():
    """主函数"""
    print("=" * 60)
    print("📦 更新包生成器")
    print("=" * 60)
    
    # 获取基础路径
    if len(sys.argv) > 1:
        base_path = sys.argv[1]
    else:
        base_path = input("请输入项目根目录路径（回车使用当前目录）: ").strip()
        if not base_path:
            base_path = "."
    
    try:
        generator = UpdatePackageGenerator(base_path)
        update_package = generator.generate_update()
        
        if update_package:
            print(f"\\n🎉 更新包生成成功！")
            print(f"📦 文件位置: {update_package}")
            print("\\n💡 使用说明:")
            print("   1. 将更新包发送给用户")
            print("   2. 用户运行UpdateTool.exe应用更新")
        else:
            print("\\nℹ️ 没有文件变更，无需更新")
            
    except Exception as e:
        print(f"\\n❌ 生成失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    # 写入生成器脚本
    generator_path = update_tools_dir / "generate_update.py"
    with open(generator_path, 'w', encoding='utf-8') as f:
        f.write(generator_script)
    
    print("✅ 更新包生成器已创建")
    
    return True

def create_update_tool():
    """创建用户更新工具"""
    project_root = Path(__file__).parent
    build_dir = project_root / "build_portable"
    package_dir = build_dir / "CompanyPolicySystem"
    
    print("🔧 创建用户更新工具...")
    
    # 用户更新工具脚本
    update_tool_script = '''#!/usr/bin/env python3
"""
用户更新工具
用于应用管理员生成的更新包
"""
import os
import sys
import json
import zipfile
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from pathlib import Path
from datetime import datetime

class UpdateTool:
    """更新工具"""
    
    def __init__(self):
        self.app_path = Path(__file__).parent
        self.backup_dir = self.app_path / "backup"
        
        # 创建GUI
        self.root = tk.Tk()
        self.root.title("公司制度查询平台 - 更新工具")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(self.root, text="公司制度查询平台 - 更新工具", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明文本
        info_text = """
请选择管理员提供的更新包文件（.zip格式）
更新工具将自动备份现有数据并应用更新
        """
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT)
        info_label.pack(pady=5)
        
        # 文件选择框架
        file_frame = tk.Frame(self.root)
        file_frame.pack(pady=10, padx=20, fill=tk.X)
        
        tk.Label(file_frame, text="更新包文件:").pack(anchor=tk.W)
        
        self.file_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.file_var, width=60)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = tk.Button(file_frame, text="浏览", command=self.browse_file)
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(pady=10, padx=20, fill=tk.X)
        
        # 日志文本框
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        tk.Label(log_frame, text="更新日志:").pack(anchor=tk.W)
        
        self.log_text = tk.Text(log_frame, height=10, width=70)
        scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=10)
        
        self.update_btn = tk.Button(btn_frame, text="开始更新", 
                                   command=self.start_update, bg="#4CAF50", fg="white")
        self.update_btn.pack(side=tk.LEFT, padx=5)
        
        self.close_btn = tk.Button(btn_frame, text="关闭", command=self.root.quit)
        self.close_btn.pack(side=tk.LEFT, padx=5)
    
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择更新包文件",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
    
    def log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
    
    def create_backup(self) -> bool:
        """创建备份"""
        try:
            self.log("创建数据备份...")
            
            # 创建备份目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"backup_{timestamp}"
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 备份关键目录
            dirs_to_backup = ["data", "docs"]
            
            for dir_name in dirs_to_backup:
                src_dir = self.app_path / dir_name
                if src_dir.exists():
                    dst_dir = backup_path / dir_name
                    shutil.copytree(src_dir, dst_dir)
                    self.log(f"已备份: {dir_name}")
            
            self.log(f"备份完成: {backup_path}")
            return True
            
        except Exception as e:
            self.log(f"备份失败: {e}")
            return False
    
    def apply_update(self, update_file: str) -> bool:
        """应用更新"""
        try:
            self.log(f"开始应用更新: {Path(update_file).name}")
            
            with zipfile.ZipFile(update_file, 'r') as zipf:
                # 读取更新信息
                if "update_info.json" in zipf.namelist():
                    update_info = json.loads(zipf.read("update_info.json").decode('utf-8'))
                    self.log(f"更新版本: {update_info.get('version', 'Unknown')}")
                    self.log(f"更新文件数: {update_info.get('total_files', 0)}")
                
                # 解压文件
                for file_info in zipf.infolist():
                    if file_info.filename != "update_info.json":
                        # 确定目标路径
                        target_path = self.app_path / file_info.filename
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 解压文件
                        with zipf.open(file_info) as source, open(target_path, 'wb') as target:
                            shutil.copyfileobj(source, target)
                        
                        self.log(f"已更新: {file_info.filename}")
            
            self.log("更新应用完成")
            return True
            
        except Exception as e:
            self.log(f"更新失败: {e}")
            return False
    
    def start_update(self):
        """开始更新"""
        update_file = self.file_var.get().strip()
        
        if not update_file:
            messagebox.showerror("错误", "请选择更新包文件")
            return
        
        if not Path(update_file).exists():
            messagebox.showerror("错误", "更新包文件不存在")
            return
        
        # 确认更新
        if not messagebox.askyesno("确认更新", 
                                  "开始更新前将自动备份现有数据。\\n确定要继续吗？"):
            return
        
        # 禁用按钮
        self.update_btn.config(state=tk.DISABLED)
        self.progress.start()
        
        try:
            # 创建备份
            if not self.create_backup():
                messagebox.showerror("错误", "备份失败，更新已取消")
                return
            
            # 应用更新
            if self.apply_update(update_file):
                self.log("\\n🎉 更新完成！")
                messagebox.showinfo("成功", "更新完成！\\n请重启应用程序以使更新生效。")
            else:
                messagebox.showerror("错误", "更新失败，请检查日志")
                
        finally:
            self.progress.stop()
            self.update_btn.config(state=tk.NORMAL)
    
    def run(self):
        """运行更新工具"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = UpdateTool()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"更新工具启动失败: {e}")

if __name__ == "__main__":
    main()
'''
    
    # 写入更新工具脚本
    update_tool_path = package_dir / "UpdateTool.py"
    with open(update_tool_path, 'w', encoding='utf-8') as f:
        f.write(update_tool_script)
    
    # 创建更新工具启动脚本
    update_tool_bat = '''@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

:: 设置应用根路径
set "APP_PATH=%~dp0"
set "APP_PATH=!APP_PATH:~0,-1!"

echo 🔧 启动更新工具...

:: 激活虚拟环境
call "!APP_PATH!\\venv\\Scripts\\activate.bat"

:: 启动更新工具
python "!APP_PATH!\\UpdateTool.py"

endlocal
'''
    
    update_tool_bat_path = package_dir / "UpdateTool.bat"
    with open(update_tool_bat_path, 'w', encoding='utf-8') as f:
        f.write(update_tool_bat)
    
    print("✅ 用户更新工具已创建")
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔄 创建增量更新系统")
    print("=" * 60)
    
    success = True
    
    if not create_update_generator():
        success = False
    
    if not create_update_tool():
        success = False
    
    if success:
        print("\\n✅ 增量更新系统创建成功！")
        print("\\n💡 使用说明:")
        print("   管理员工具: build_portable/update_tools/generate_update.py")
        print("   用户工具: CompanyPolicySystem/UpdateTool.bat")
    else:
        print("\\n❌ 创建失败！")

if __name__ == "__main__":
    main()
