#!/usr/bin/env python3
"""
公司制度查询平台 - 一键构建和测试脚本
实现零配置交付包的完整构建和验证流程
"""
import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    公司制度查询平台                          ║
║                  零配置交付包构建系统                        ║
║                                                              ║
║  🏢 AI问答功能正常运行                                       ║
║  🔍 制度检索功能完整可用                                     ║
║  💾 向量数据库开箱即用                                       ║
║  📄 文档预览精准定位                                         ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def run_command(command: list, description: str, timeout: int = 300) -> bool:
    """运行命令"""
    print(f"\n📋 {description}")
    print("-" * 60)

    try:
        start_time = time.time()

        # 运行命令，使用更兼容的编码处理
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            errors='replace',
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                output_lines.append(output.strip())
                
                # 检查超时
                if time.time() - start_time > timeout:
                    process.terminate()
                    print(f"⚠️ 命令超时 ({timeout}秒)，已终止")
                    return False
        
        # 获取返回码
        return_code = process.poll()
        elapsed_time = time.time() - start_time
        
        if return_code == 0:
            print(f"✅ {description} - 完成 ({elapsed_time:.1f}秒)")
            return True
        else:
            print(f"❌ {description} - 失败 (返回码: {return_code})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_system_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    checks = []
    
    # Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        checks.append(True)
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        checks.append(False)
    
    # 磁盘空间
    try:
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**3)  # GB
        if free_space >= 5:
            print(f"✅ 磁盘空间: {free_space:.1f}GB")
            checks.append(True)
        else:
            print(f"❌ 磁盘空间不足: {free_space:.1f}GB (需要至少5GB)")
            checks.append(False)
    except Exception:
        print("⚠️ 无法检查磁盘空间")
        checks.append(True)
    
    # 内存
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb >= 4:
            print(f"✅ 系统内存: {memory_gb:.1f}GB")
            checks.append(True)
        else:
            print(f"⚠️ 系统内存较低: {memory_gb:.1f}GB (推荐8GB以上)")
            checks.append(True)  # 不阻止构建
    except ImportError:
        print("⚠️ 无法检查系统内存 (需要安装psutil)")
        checks.append(True)
    
    # 网络连接
    try:
        import urllib.request
        urllib.request.urlopen('https://www.python.org', timeout=5)
        print("✅ 网络连接正常")
        checks.append(True)
    except Exception:
        print("⚠️ 网络连接异常，可能影响依赖下载")
        checks.append(True)  # 不阻止构建
    
    return all(checks)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='公司制度查询平台构建系统')
    parser.add_argument('--build-only', action='store_true', help='仅构建，不测试')
    parser.add_argument('--test-only', action='store_true', help='仅测试，不构建')
    parser.add_argument('--skip-deps', action='store_true', help='跳过依赖安装（加速构建）')
    parser.add_argument('--timeout', type=int, default=1800, help='单步超时时间（秒）')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败")
        return 1
    
    project_root = Path(__file__).parent
    build_success = True
    test_success = True
    
    start_time = time.time()
    
    try:
        # 构建阶段
        if not args.test_only:
            print("\n" + "="*60)
            print("🔨 开始构建阶段")
            print("="*60)
            
            build_command = [sys.executable, str(project_root / "build_complete_package.py")]
            build_success = run_command(build_command, "完整包构建", args.timeout)
            
            if not build_success:
                print("\n❌ 构建失败，停止后续步骤")
                return 1
        
        # 测试阶段
        if not args.build_only:
            print("\n" + "="*60)
            print("🧪 开始测试阶段")
            print("="*60)
            
            test_command = [sys.executable, str(project_root / "test_package.py")]
            test_success = run_command(test_command, "包验证测试", args.timeout)
        
        # 总结
        elapsed_time = time.time() - start_time
        
        print("\n" + "="*60)
        print("📊 构建和测试总结")
        print("="*60)
        print(f"⏱️ 总耗时: {elapsed_time/60:.1f}分钟")
        
        if not args.test_only:
            print(f"🔨 构建结果: {'✅ 成功' if build_success else '❌ 失败'}")
        
        if not args.build_only:
            print(f"🧪 测试结果: {'✅ 通过' if test_success else '❌ 失败'}")
        
        # 最终状态
        overall_success = build_success and test_success
        
        if overall_success:
            print("\n🎉 构建和测试全部成功！")
            print("\n📦 交付物位置:")
            print(f"   便携包: build_portable/CompanyPolicySystem/")
            print(f"   安装脚本: build_portable/installer/installer.nsi")
            print(f"   更新工具: build_portable/update_tools/")
            
            print("\n💡 下一步操作:")
            print("1. 使用NSIS编译安装程序")
            print("2. 在干净环境中部署测试")
            print("3. 准备用户培训材料")
            
            return 0
        else:
            print("\n❌ 构建或测试失败！")
            print("\n🔍 故障排除:")
            print("1. 检查错误日志")
            print("2. 确认系统环境")
            print("3. 重试单个步骤")
            
            return 1
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断操作")
        return 0
    except Exception as e:
        print(f"\n❌ 执行异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
