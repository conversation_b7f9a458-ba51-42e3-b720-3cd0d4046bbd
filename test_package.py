#!/usr/bin/env python3
"""
打包验证测试脚本
在干净环境中测试安装包的核心功能
"""
import os
import sys
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Any

class PackageValidator:
    """包验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_portable"
        self.package_dir = self.build_dir / "CompanyPolicySystem"
        
        self.test_results = {
            "environment": {},
            "files": {},
            "functionality": {},
            "performance": {},
            "overall": False
        }
        
        print("🧪 公司制度查询平台 - 包验证测试")
        print("=" * 60)
    
    def test_environment(self) -> bool:
        """测试环境检查"""
        print("\n🔍 环境检查测试...")
        
        tests = []
        
        # 检查包目录存在
        if self.package_dir.exists():
            print("✅ 包目录存在")
            tests.append(True)
        else:
            print("❌ 包目录不存在")
            tests.append(False)
            return False
        
        # 检查Python环境
        python_exe = self.package_dir / "venv" / "Scripts" / "python.exe"
        if python_exe.exists():
            print("✅ Python虚拟环境存在")
            tests.append(True)
            
            # 测试Python版本
            try:
                result = subprocess.run([
                    str(python_exe), "--version"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"✅ Python版本: {version}")
                    self.test_results["environment"]["python_version"] = version
                    tests.append(True)
                else:
                    print("❌ Python版本检查失败")
                    tests.append(False)
                    
            except Exception as e:
                print(f"❌ Python版本检查异常: {e}")
                tests.append(False)
        else:
            print("❌ Python虚拟环境不存在")
            tests.append(False)
        
        # 检查关键依赖
        dependencies_to_check = [
            "torch",
            "transformers", 
            "sentence_transformers",
            "chromadb",
            "PyQt6"
        ]
        
        for dep in dependencies_to_check:
            try:
                result = subprocess.run([
                    str(python_exe), "-c", f"import {dep}; print('{dep}: OK')"
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print(f"✅ {dep} 可用")
                    tests.append(True)
                else:
                    print(f"❌ {dep} 不可用")
                    tests.append(False)
                    
            except Exception as e:
                print(f"❌ {dep} 检查异常: {e}")
                tests.append(False)
        
        success = all(tests)
        self.test_results["environment"]["success"] = success
        return success
    
    def test_file_structure(self) -> bool:
        """测试文件结构"""
        print("\n📁 文件结构测试...")
        
        required_files = [
            "main.py",
            "config.yaml",
            "start.bat",
            "start_debug.bat", 
            "check_environment.bat",
            "UpdateTool.py",
            "UpdateTool.bat",
            "README.md",
            "version.json"
        ]
        
        required_dirs = [
            "src/",
            "data/",
            "models/",
            "docs/",
            "venv/",
            "static/",
            "logs/"
        ]
        
        tests = []
        
        # 检查必要文件
        for file_path in required_files:
            full_path = self.package_dir / file_path
            if full_path.exists():
                print(f"✅ {file_path}")
                tests.append(True)
            else:
                print(f"❌ {file_path} - 缺失")
                tests.append(False)
        
        # 检查必要目录
        for dir_path in required_dirs:
            full_path = self.package_dir / dir_path
            if full_path.exists() and full_path.is_dir():
                print(f"✅ {dir_path}")
                tests.append(True)
            else:
                print(f"❌ {dir_path} - 缺失")
                tests.append(False)
        
        success = all(tests)
        self.test_results["files"]["success"] = success
        self.test_results["files"]["total_files"] = len(required_files)
        self.test_results["files"]["total_dirs"] = len(required_dirs)
        
        return success
    
    def test_startup_scripts(self) -> bool:
        """测试启动脚本"""
        print("\n🚀 启动脚本测试...")
        
        tests = []
        
        # 测试环境检查脚本
        check_script = self.package_dir / "check_environment.bat"
        if check_script.exists():
            try:
                print("测试环境检查脚本...")
                result = subprocess.run([
                    str(check_script)
                ], capture_output=True, text=True, timeout=60, cwd=str(self.package_dir))
                
                if "Python环境检查" in result.stdout:
                    print("✅ 环境检查脚本正常")
                    tests.append(True)
                else:
                    print("❌ 环境检查脚本异常")
                    tests.append(False)
                    
            except Exception as e:
                print(f"❌ 环境检查脚本测试失败: {e}")
                tests.append(False)
        else:
            print("❌ 环境检查脚本不存在")
            tests.append(False)
        
        # 测试路径检查脚本
        path_check_script = self.package_dir / "check_paths.py"
        if path_check_script.exists():
            try:
                python_exe = self.package_dir / "venv" / "Scripts" / "python.exe"
                print("测试路径检查脚本...")
                
                # 设置环境变量
                env = os.environ.copy()
                env["APP_PATH"] = str(self.package_dir)
                
                result = subprocess.run([
                    str(python_exe), str(path_check_script)
                ], capture_output=True, text=True, timeout=30, env=env)
                
                if "路径检查" in result.stdout:
                    print("✅ 路径检查脚本正常")
                    tests.append(True)
                else:
                    print("❌ 路径检查脚本异常")
                    tests.append(False)
                    
            except Exception as e:
                print(f"❌ 路径检查脚本测试失败: {e}")
                tests.append(False)
        else:
            print("⚠️ 路径检查脚本不存在（可选）")
            tests.append(True)
        
        success = all(tests)
        self.test_results["functionality"]["startup_scripts"] = success
        return success
    
    def test_configuration(self) -> bool:
        """测试配置加载"""
        print("\n⚙️ 配置加载测试...")
        
        python_exe = self.package_dir / "venv" / "Scripts" / "python.exe"
        
        # 测试配置加载
        test_config_script = '''
import sys
import os
sys.path.insert(0, ".")

# 设置环境变量
os.environ["APP_PATH"] = "."

try:
    from src.utils.config import get_config
    config = get_config()
    
    print(f"配置加载成功")
    print(f"窗口标题: {config.ui.window_title}")
    print(f"数据目录: {config.data_dir}")
    print(f"向量数据库: {config.database.chroma_persist_dir}")
    print(f"模型目录: {config.models_dir}")
    
    # 检查路径是否为绝对路径
    import os
    if os.path.isabs(config.database.chroma_persist_dir):
        print("✅ 向量数据库使用绝对路径")
    else:
        print("❌ 向量数据库未使用绝对路径")
        
    print("CONFIG_TEST_SUCCESS")
    
except Exception as e:
    print(f"配置加载失败: {e}")
    import traceback
    traceback.print_exc()
'''
        
        try:
            result = subprocess.run([
                str(python_exe), "-c", test_config_script
            ], capture_output=True, text=True, timeout=30, cwd=str(self.package_dir))
            
            if "CONFIG_TEST_SUCCESS" in result.stdout:
                print("✅ 配置加载测试通过")
                self.test_results["functionality"]["config_loading"] = True
                return True
            else:
                print("❌ 配置加载测试失败")
                print("输出:", result.stdout)
                print("错误:", result.stderr)
                self.test_results["functionality"]["config_loading"] = False
                return False
                
        except Exception as e:
            print(f"❌ 配置加载测试异常: {e}")
            self.test_results["functionality"]["config_loading"] = False
            return False
    
    def test_ai_components(self) -> bool:
        """测试AI组件"""
        print("\n🤖 AI组件测试...")
        
        python_exe = self.package_dir / "venv" / "Scripts" / "python.exe"
        
        # 测试AI模型加载
        test_ai_script = '''
import sys
import os
sys.path.insert(0, ".")

# 设置环境变量
os.environ["APP_PATH"] = "."

try:
    from src.core.ai_model import get_ai_model
    
    print("开始AI模型测试...")
    ai_model = get_ai_model()
    
    # 测试简单生成
    response = ai_model.generate_response("你好", max_length=50)
    print(f"AI响应: {response[:100]}...")
    
    print("AI_TEST_SUCCESS")
    
except Exception as e:
    print(f"AI模型测试失败: {e}")
    import traceback
    traceback.print_exc()
'''
        
        try:
            print("测试AI模型加载（可能需要较长时间）...")
            result = subprocess.run([
                str(python_exe), "-c", test_ai_script
            ], capture_output=True, text=True, timeout=120, cwd=str(self.package_dir))
            
            if "AI_TEST_SUCCESS" in result.stdout:
                print("✅ AI组件测试通过")
                self.test_results["functionality"]["ai_components"] = True
                return True
            else:
                print("❌ AI组件测试失败")
                print("输出:", result.stdout[-500:])  # 只显示最后500字符
                print("错误:", result.stderr[-500:])
                self.test_results["functionality"]["ai_components"] = False
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️ AI组件测试超时（可能是首次模型下载）")
            self.test_results["functionality"]["ai_components"] = "timeout"
            return True  # 超时不算失败
        except Exception as e:
            print(f"❌ AI组件测试异常: {e}")
            self.test_results["functionality"]["ai_components"] = False
            return False
    
    def generate_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        report = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "package_path": str(self.package_dir),
            "results": self.test_results
        }
        
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            if category == "overall":
                continue
            if isinstance(results, dict):
                for test_name, result in results.items():
                    total_tests += 1
                    if result is True or result == "timeout":
                        passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        report["success_rate"] = success_rate
        
        # 保存报告
        report_path = self.build_dir / "test_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存: {report_path}")
        print(f"📈 测试成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        return success_rate >= 80  # 80%以上算通过
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 开始完整验证测试...")
        start_time = time.time()
        
        test_steps = [
            ("环境检查", self.test_environment),
            ("文件结构", self.test_file_structure),
            ("启动脚本", self.test_startup_scripts),
            ("配置加载", self.test_configuration),
            ("AI组件", self.test_ai_components)
        ]
        
        results = []
        
        for step_name, test_func in test_steps:
            print(f"\n{'='*20} {step_name} {'='*20}")
            try:
                result = test_func()
                results.append(result)
                print(f"{'✅' if result else '❌'} {step_name} - {'通过' if result else '失败'}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                results.append(False)
        
        # 生成报告
        overall_success = self.generate_report()
        
        elapsed_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("🏁 验证测试完成")
        print(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
        print(f"📊 结果: {'✅ 通过' if overall_success else '❌ 失败'}")
        
        return overall_success

def main():
    """主函数"""
    try:
        validator = PackageValidator()
        
        if validator.run_all_tests():
            print("\n🎉 包验证测试通过！")
            print("💡 可以进行部署测试")
            sys.exit(0)
        else:
            print("\n❌ 包验证测试失败！")
            print("💡 请检查构建过程和测试报告")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
