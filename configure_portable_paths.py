#!/usr/bin/env python3
"""
配置便携环境的绝对路径
修改向量数据库和其他关键路径配置，确保在打包环境中正常工作
"""
import os
import sys
from pathlib import Path
import shutil

def configure_portable_paths():
    """配置便携环境路径"""
    project_root = Path(__file__).parent
    build_dir = project_root / "build_portable"
    package_dir = build_dir / "CompanyPolicySystem"
    
    print("🔧 配置便携环境路径...")
    
    # 1. 修改config.py以支持便携环境
    config_py_path = package_dir / "src" / "utils" / "config.py"
    
    if not config_py_path.exists():
        print(f"❌ 配置文件不存在: {config_py_path}")
        return False
    
    # 读取原始配置文件
    with open(config_py_path, 'r', encoding='utf-8') as f:
        config_content = f.read()
    
    # 修改ConfigManager的__init__方法以支持便携环境
    portable_init_code = '''    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"

        # 获取程序根目录 - 支持便携环境
        if getattr(sys, 'frozen', False):
            # 打包后的程序
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller环境
                self.base_dir = Path(sys.executable).parent
            else:
                # 便携Python环境
                self.base_dir = Path(sys.executable).parent.parent
        else:
            # 开发环境
            self.base_dir = Path(__file__).parent.parent.parent
        
        # 支持环境变量覆盖路径
        if 'APP_PATH' in os.environ:
            self.base_dir = Path(os.environ['APP_PATH'])
        
        # 确保关键目录存在
        self._ensure_directories()

        self._config: Optional[AppConfig] = None'''
    
    # 替换__init__方法
    import re
    pattern = r'def __init__\(self, config_path: Optional\[str\] = None\):.*?self\._config: Optional\[AppConfig\] = None'
    config_content = re.sub(pattern, portable_init_code, config_content, flags=re.DOTALL)
    
    # 添加目录确保方法
    ensure_dirs_method = '''
    def _ensure_directories(self):
        """确保必要目录存在"""
        required_dirs = [
            "data",
            "data/chroma_db", 
            "data/whoosh_index",
            "data/preview_pdfs",
            "data/sessions",
            "logs",
            "models",
            "docs"
        ]
        
        for dir_path in required_dirs:
            full_path = self.base_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            
            # 设置目录权限（Windows）
            try:
                import stat
                os.chmod(full_path, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
            except Exception:
                pass  # 权限设置失败不影响程序运行
'''
    
    # 在类定义后添加目录确保方法
    config_content = config_content.replace(
        'def load_config(self) -> AppConfig:',
        ensure_dirs_method + '\n    def load_config(self) -> AppConfig:'
    )
    
    # 写回修改后的配置文件
    with open(config_py_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 配置文件路径支持已更新")
    
    # 2. 创建便携环境专用的配置文件
    portable_config_content = '''# 公司制度查询平台配置文件 - 便携版本

# 模型配置
model:
  embedding_model: "shibing624/text2vec-base-chinese"
  llm_model: "qwen-1.5-1.8b-chat"
  max_tokens: 512
  temperature: 0.7
  device: "auto"

# 数据库配置 - 使用相对路径，运行时转换为绝对路径
database:
  chroma_persist_dir: "data/chroma_db"
  whoosh_index_dir: "data/whoosh_index"
  collection_name: "policy_documents"

# 文档处理配置
processing:
  chunk_size: 512
  chunk_overlap: 50
  supported_formats:
    - ".pdf"
    - ".docx"
    - ".doc"
    - ".xlsx"
    - ".xls"
    - ".txt"
  ocr_language: "chi_sim+eng"

# 界面配置
ui:
  window_title: "公司制度查询平台 v1.0"
  window_width: 1200
  window_height: 800
  theme: "light"

# 路径配置 - 相对路径，运行时转换
docs_dir: "docs"
data_dir: "data"
models_dir: "models"
preview_dir: "data/preview_pdfs"

# 性能配置
max_memory_gb: 8
enable_cache: true
cache_size: 1000

# 日志配置
log_level: "INFO"
log_file: "logs/app.log"

# 便携环境特殊配置
portable_mode: true
auto_create_dirs: true
'''
    
    config_yaml_path = package_dir / "config.yaml"
    with open(config_yaml_path, 'w', encoding='utf-8') as f:
        f.write(portable_config_content)
    
    print("✅ 便携环境配置文件已创建")
    
    # 3. 修改向量存储以支持绝对路径
    vector_store_path = package_dir / "src" / "core" / "vector_store.py"
    
    if vector_store_path.exists():
        with open(vector_store_path, 'r', encoding='utf-8') as f:
            vector_content = f.read()
        
        # 确保向量存储使用绝对路径
        vector_content = vector_content.replace(
            'self.persist_directory = self.config.database.chroma_persist_dir',
            '''# 确保使用绝对路径
        persist_dir = self.config.database.chroma_persist_dir
        if not os.path.isabs(persist_dir):
            persist_dir = os.path.join(self.config.base_dir, persist_dir)
        self.persist_directory = persist_dir
        
        # 确保目录存在
        os.makedirs(self.persist_directory, exist_ok=True)'''
        )
        
        with open(vector_store_path, 'w', encoding='utf-8') as f:
            f.write(vector_content)
        
        print("✅ 向量存储路径配置已更新")
    
    # 4. 创建路径检查脚本
    path_check_script = '''#!/usr/bin/env python3
"""
路径检查脚本 - 验证便携环境路径配置
"""
import os
import sys
from pathlib import Path

def check_paths():
    """检查路径配置"""
    print("🔍 检查便携环境路径配置...")
    
    # 获取应用根目录
    if 'APP_PATH' in os.environ:
        app_path = Path(os.environ['APP_PATH'])
    else:
        app_path = Path(__file__).parent
    
    print(f"📁 应用根目录: {app_path}")
    
    # 检查关键路径
    paths_to_check = {
        "配置文件": app_path / "config.yaml",
        "主程序": app_path / "main.py",
        "源代码": app_path / "src",
        "数据目录": app_path / "data",
        "向量数据库": app_path / "data" / "chroma_db",
        "全文索引": app_path / "data" / "whoosh_index",
        "模型目录": app_path / "models",
        "文档目录": app_path / "docs",
        "虚拟环境": app_path / "venv",
        "Python解释器": app_path / "venv" / "Scripts" / "python.exe"
    }
    
    all_ok = True
    for name, path in paths_to_check.items():
        if path.exists():
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path} (不存在)")
            all_ok = False
    
    # 检查环境变量
    print("\\n🌍 环境变量检查:")
    env_vars = ["APP_PATH", "VECTOR_DB_PATH", "MODELS_PATH", "DOCS_PATH"]
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"   {var}: {value}")
    
    if all_ok:
        print("\\n✅ 所有路径检查通过！")
    else:
        print("\\n❌ 部分路径检查失败，请检查安装")
    
    return all_ok

if __name__ == "__main__":
    check_paths()
'''
    
    path_check_path = package_dir / "check_paths.py"
    with open(path_check_path, 'w', encoding='utf-8') as f:
        f.write(path_check_script)
    
    print("✅ 路径检查脚本已创建")
    
    print("🎉 便携环境路径配置完成！")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 配置便携环境绝对路径")
    print("=" * 60)
    
    if configure_portable_paths():
        print("\\n✅ 配置成功！")
    else:
        print("\\n❌ 配置失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
