#!/usr/bin/env python3
"""
简单构建测试
"""
import os
import sys
import subprocess
from pathlib import Path

def test_simple_build():
    """简单构建测试"""
    print("🔍 简单构建测试...")
    
    project_root = Path(__file__).parent
    print(f"项目根目录: {project_root}")
    
    # 测试运行第一个脚本
    script_path = project_root / "build_portable_package.py"
    print(f"测试脚本: {script_path}")
    
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    try:
        print("开始运行脚本...")
        
        # 使用更简单的方式运行
        result = subprocess.run([
            sys.executable, str(script_path)
        ], cwd=str(project_root), timeout=300)
        
        print(f"脚本执行完成，返回码: {result.returncode}")
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时")
        return False
    except Exception as e:
        print(f"❌ 脚本执行异常: {e}")
        return False

if __name__ == "__main__":
    test_simple_build()
