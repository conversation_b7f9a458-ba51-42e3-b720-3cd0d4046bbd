#!/usr/bin/env python3
"""
测试编码问题
"""
import os
import sys
from pathlib import Path

def test_encoding():
    """测试编码"""
    print("🔍 测试编码问题...")
    
    # 测试当前目录
    current_dir = Path(".")
    print(f"当前目录: {current_dir.absolute()}")
    
    # 测试文件列表
    try:
        files = list(current_dir.iterdir())
        print(f"文件数量: {len(files)}")
        
        for file_path in files:
            try:
                print(f"文件: {file_path.name}")
            except UnicodeEncodeError as e:
                print(f"编码错误: {e}")
                
    except Exception as e:
        print(f"目录遍历错误: {e}")
    
    # 测试环境变量
    print(f"系统编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    # 测试中文路径
    docs_dir = Path("docs")
    if docs_dir.exists():
        print("测试docs目录...")
        try:
            for item in docs_dir.iterdir():
                print(f"  {item.name}")
        except Exception as e:
            print(f"docs目录遍历错误: {e}")

if __name__ == "__main__":
    test_encoding()
