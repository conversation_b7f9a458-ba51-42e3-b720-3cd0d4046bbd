#!/usr/bin/env python3
"""
创建启动脚本和环境配置
"""
import os
from pathlib import Path

def create_startup_scripts():
    """创建启动脚本"""
    project_root = Path(__file__).parent
    build_dir = project_root / "build_portable"
    package_dir = build_dir / "CompanyPolicySystem"
    
    # 创建start.bat启动脚本
    start_bat_content = '''@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

:: 设置应用根路径
set "APP_PATH=%~dp0"
set "APP_PATH=!APP_PATH:~0,-1!"

echo 🏢 公司制度查询平台启动中...
echo 📁 应用路径: !APP_PATH!

:: 设置环境变量
set "PYTHONPATH=!APP_PATH!"
set "VECTOR_DB_PATH=!APP_PATH!\\data\\chroma_db"
set "WHOOSH_INDEX_PATH=!APP_PATH!\\data\\whoosh_index"
set "MODELS_PATH=!APP_PATH!\\models"
set "DOCS_PATH=!APP_PATH!\\docs"
set "PREVIEW_PATH=!APP_PATH!\\data\\preview_pdfs"

:: 性能优化环境变量
set "OMP_NUM_THREADS=4"
set "MKL_NUM_THREADS=4"
set "NUMEXPR_NUM_THREADS=4"
set "OPENBLAS_NUM_THREADS=4"
set "TORCH_NUM_THREADS=4"
set "MKL_ENABLE_INSTRUCTIONS=AVX2"

:: 检查必要目录
if not exist "!APP_PATH!\\data" mkdir "!APP_PATH!\\data"
if not exist "!APP_PATH!\\data\\chroma_db" mkdir "!APP_PATH!\\data\\chroma_db"
if not exist "!APP_PATH!\\data\\whoosh_index" mkdir "!APP_PATH!\\data\\whoosh_index"
if not exist "!APP_PATH!\\data\\preview_pdfs" mkdir "!APP_PATH!\\data\\preview_pdfs"
if not exist "!APP_PATH!\\data\\sessions" mkdir "!APP_PATH!\\data\\sessions"
if not exist "!APP_PATH!\\logs" mkdir "!APP_PATH!\\logs"

:: 激活虚拟环境
echo 🐍 激活Python虚拟环境...
call "!APP_PATH!\\venv\\Scripts\\activate.bat"

:: 检查Python环境
python --version
if errorlevel 1 (
    echo ❌ Python环境异常，请检查安装
    pause
    exit /b 1
)

:: 启动主程序
echo 🚀 启动应用程序...
cd /d "!APP_PATH!"
python main.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出，错误代码: %errorlevel%
    echo 📋 请检查日志文件: logs\\app.log
    echo.
    pause
)

endlocal
'''
    
    # 创建start_debug.bat调试启动脚本
    start_debug_bat_content = '''@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

:: 设置应用根路径
set "APP_PATH=%~dp0"
set "APP_PATH=!APP_PATH:~0,-1!"

echo 🔧 公司制度查询平台 - 调试模式
echo 📁 应用路径: !APP_PATH!

:: 设置环境变量
set "PYTHONPATH=!APP_PATH!"
set "VECTOR_DB_PATH=!APP_PATH!\\data\\chroma_db"
set "WHOOSH_INDEX_PATH=!APP_PATH!\\data\\whoosh_index"
set "MODELS_PATH=!APP_PATH!\\models"
set "DOCS_PATH=!APP_PATH!\\docs"
set "PREVIEW_PATH=!APP_PATH!\\data\\preview_pdfs"

:: 调试环境变量
set "PYTHONUNBUFFERED=1"
set "TORCH_SHOW_CPP_STACKTRACES=1"

:: 性能优化环境变量
set "OMP_NUM_THREADS=4"
set "MKL_NUM_THREADS=4"
set "NUMEXPR_NUM_THREADS=4"
set "OPENBLAS_NUM_THREADS=4"
set "TORCH_NUM_THREADS=4"

:: 检查必要目录
echo 📁 检查目录结构...
if not exist "!APP_PATH!\\data" mkdir "!APP_PATH!\\data"
if not exist "!APP_PATH!\\data\\chroma_db" mkdir "!APP_PATH!\\data\\chroma_db"
if not exist "!APP_PATH!\\data\\whoosh_index" mkdir "!APP_PATH!\\data\\whoosh_index"
if not exist "!APP_PATH!\\data\\preview_pdfs" mkdir "!APP_PATH!\\data\\preview_pdfs"
if not exist "!APP_PATH!\\data\\sessions" mkdir "!APP_PATH!\\data\\sessions"
if not exist "!APP_PATH!\\logs" mkdir "!APP_PATH!\\logs"

:: 显示环境信息
echo.
echo 🔍 环境信息:
echo    Python路径: !APP_PATH!\\venv\\Scripts\\python.exe
echo    向量数据库: !VECTOR_DB_PATH!
echo    全文索引: !WHOOSH_INDEX_PATH!
echo    模型目录: !MODELS_PATH!
echo    文档目录: !DOCS_PATH!
echo.

:: 激活虚拟环境
echo 🐍 激活Python虚拟环境...
call "!APP_PATH!\\venv\\Scripts\\activate.bat"

:: 检查Python环境
echo 🔍 Python环境检查:
python --version
python -c "import sys; print(f'Python路径: {sys.executable}')"
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')" 2>nul || echo "⚠️ PyTorch未安装或有问题"
python -c "import transformers; print(f'Transformers版本: {transformers.__version__}')" 2>nul || echo "⚠️ Transformers未安装或有问题"

echo.
echo 🚀 启动应用程序（调试模式）...
cd /d "!APP_PATH!"
python main.py

:: 程序退出后保持窗口打开
echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul

endlocal
'''
    
    # 创建check_environment.bat环境检查脚本
    check_env_bat_content = '''@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

:: 设置应用根路径
set "APP_PATH=%~dp0"
set "APP_PATH=!APP_PATH:~0,-1!"

echo 🔍 公司制度查询平台 - 环境检查
echo ================================================
echo 📁 应用路径: !APP_PATH!
echo.

:: 检查目录结构
echo 📂 目录结构检查:
for %%d in (data models docs venv src static) do (
    if exist "!APP_PATH!\\%%d" (
        echo    ✅ %%d 目录存在
    ) else (
        echo    ❌ %%d 目录缺失
    )
)

echo.
echo 📄 关键文件检查:
for %%f in (main.py config.yaml) do (
    if exist "!APP_PATH!\\%%f" (
        echo    ✅ %%f 文件存在
    ) else (
        echo    ❌ %%f 文件缺失
    )
)

:: 检查Python环境
echo.
echo 🐍 Python环境检查:
if exist "!APP_PATH!\\venv\\Scripts\\python.exe" (
    echo    ✅ Python虚拟环境存在
    
    :: 激活虚拟环境并检查
    call "!APP_PATH!\\venv\\Scripts\\activate.bat"
    
    python --version
    echo    Python路径: !APP_PATH!\\venv\\Scripts\\python.exe
    
    echo.
    echo 📦 关键依赖包检查:
    python -c "import torch; print(f'    ✅ PyTorch: {torch.__version__}')" 2>nul || echo "    ❌ PyTorch未安装"
    python -c "import transformers; print(f'    ✅ Transformers: {transformers.__version__}')" 2>nul || echo "    ❌ Transformers未安装"
    python -c "import sentence_transformers; print(f'    ✅ Sentence-Transformers: {sentence_transformers.__version__}')" 2>nul || echo "    ❌ Sentence-Transformers未安装"
    python -c "import chromadb; print(f'    ✅ ChromaDB: {chromadb.__version__}')" 2>nul || echo "    ❌ ChromaDB未安装"
    python -c "from PyQt6.QtWidgets import QApplication; print('    ✅ PyQt6: 可用')" 2>nul || echo "    ❌ PyQt6未安装"
    
) else (
    echo    ❌ Python虚拟环境不存在
)

:: 检查数据目录权限
echo.
echo 🔐 数据目录权限检查:
echo test > "!APP_PATH!\\data\\test_write.tmp" 2>nul
if exist "!APP_PATH!\\data\\test_write.tmp" (
    echo    ✅ 数据目录可写
    del "!APP_PATH!\\data\\test_write.tmp" >nul 2>&1
) else (
    echo    ❌ 数据目录不可写，请检查权限
)

echo.
echo ================================================
echo 环境检查完成，按任意键退出...
pause >nul

endlocal
'''
    
    # 写入文件
    scripts = [
        ("start.bat", start_bat_content),
        ("start_debug.bat", start_debug_bat_content), 
        ("check_environment.bat", check_env_bat_content)
    ]
    
    package_dir.mkdir(parents=True, exist_ok=True)
    
    for script_name, content in scripts:
        script_path = package_dir / script_name
        script_path.write_text(content, encoding='utf-8')
        print(f"✅ 创建启动脚本: {script_name}")
    
    print("✅ 所有启动脚本创建完成")

if __name__ == "__main__":
    create_startup_scripts()
