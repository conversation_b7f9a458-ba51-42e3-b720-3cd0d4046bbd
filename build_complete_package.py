#!/usr/bin/env python3
"""
公司制度查询平台 - 完整打包构建脚本
整合所有构建步骤，生成零配置交付包
"""
import os
import sys
import subprocess
import time
from pathlib import Path

class CompletePackageBuilder:
    """完整包构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_portable"
        self.package_dir = self.build_dir / "CompanyPolicySystem"
        
        print("🏢 公司制度查询平台 - 完整打包构建")
        print("=" * 60)
        print(f"📁 项目根目录: {self.project_root}")
        print(f"🔨 构建目录: {self.build_dir}")
        print(f"📦 包目录: {self.package_dir}")
    
    def run_script(self, script_name: str, description: str) -> bool:
        """运行构建脚本"""
        print(f"\n📋 步骤: {description}")
        print("-" * 40)
        
        script_path = self.project_root / script_name
        
        if not script_path.exists():
            print(f"❌ 脚本不存在: {script_path}")
            return False
        
        try:
            # 运行脚本
            result = subprocess.run([
                sys.executable, str(script_path)
            ], check=True, capture_output=True, text=True, encoding='utf-8')
            
            # 显示输出
            if result.stdout:
                print(result.stdout)
            
            print(f"✅ {description} - 完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - 失败")
            print(f"错误代码: {e.returncode}")
            if e.stdout:
                print("标准输出:")
                print(e.stdout)
            if e.stderr:
                print("错误输出:")
                print(e.stderr)
            return False
        except Exception as e:
            print(f"❌ {description} - 异常: {e}")
            return False
    
    def check_prerequisites(self) -> bool:
        """检查构建前提条件"""
        print("\n🔍 检查构建前提条件...")
        
        checks = []
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version >= (3, 8):
            print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            checks.append(True)
        else:
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
            checks.append(False)
        
        # 检查必要文件
        required_files = [
            "main.py",
            "config.yaml",
            "requirements.txt",
            "src/",
            "build_portable_package.py",
            "create_startup_scripts.py",
            "configure_portable_paths.py",
            "create_nsis_installer.py",
            "create_update_system.py"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                print(f"✅ {file_path}")
                checks.append(True)
            else:
                print(f"❌ {file_path} - 缺失")
                checks.append(False)
        
        # 检查网络连接（用于下载Python嵌入式版本）
        try:
            import urllib.request
            urllib.request.urlopen('https://www.python.org', timeout=5)
            print("✅ 网络连接正常")
            checks.append(True)
        except Exception:
            print("⚠️ 网络连接异常，可能影响Python嵌入式版本下载")
            checks.append(True)  # 不阻止构建，只是警告
        
        return all(checks)
    
    def create_readme(self):
        """创建README文件"""
        print("\n📝 创建README文件...")
        
        readme_content = '''# 公司制度查询平台 v1.0

## 功能特性

✨ **AI智能问答** - 基于大语言模型的制度问答
🔍 **语义检索** - 向量数据库支持的语义搜索  
📄 **文档预览** - PDF文档精准定位和预览
🚀 **零配置部署** - 开箱即用，无需安装Python环境

## 系统要求

- Windows 7/8/10/11 (64位)
- 内存: 4GB以上 (推荐8GB)
- 硬盘: 2GB可用空间
- Visual C++ 2019运行库

## 安装使用

### 方式一：直接运行（推荐）
1. 解压到任意目录
2. 双击 `start.bat` 启动程序
3. 首次启动需要初始化AI模型，请耐心等待

### 方式二：安装程序
1. 运行 `CompanyPolicySystem_Setup_v1.0.0.exe`
2. 按向导完成安装
3. 从开始菜单或桌面快捷方式启动

## 故障排除

### 程序无法启动
1. 运行 `check_environment.bat` 检查环境
2. 运行 `start_debug.bat` 查看详细错误信息
3. 检查 `logs/app.log` 日志文件

### AI功能不可用
1. 确保网络连接正常（首次需要下载模型）
2. 检查系统内存是否充足
3. 尝试重启程序

### 文档检索无结果
1. 确认 `docs` 目录中有文档文件
2. 运行预处理程序重建索引
3. 检查文档格式是否支持

## 更新说明

### 增量更新
1. 获取管理员提供的更新包（.zip文件）
2. 双击 `UpdateTool.bat` 启动更新工具
3. 选择更新包文件并应用更新
4. 重启程序使更新生效

## 技术支持

如遇技术问题，请联系IT部门并提供：
- 错误截图
- 日志文件 (`logs/app.log`)
- 系统环境信息

---
© 2024 公司名称 - 内部使用版本
'''
        
        readme_path = self.package_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ README文件已创建")
    
    def create_version_info(self):
        """创建版本信息文件"""
        print("\n📋 创建版本信息...")
        
        version_info = {
            "version": "1.0.0",
            "build_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "features": [
                "AI智能问答",
                "语义检索", 
                "文档预览",
                "增量更新"
            ],
            "components": {
                "embedding_model": "shibing624/text2vec-base-chinese",
                "llm_model": "qwen-1.5-1.8b-chat",
                "vector_db": "ChromaDB",
                "ui_framework": "PyQt6"
            }
        }
        
        import json
        version_path = self.package_dir / "version.json"
        with open(version_path, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        print("✅ 版本信息文件已创建")
    
    def build(self) -> bool:
        """执行完整构建"""
        start_time = time.time()
        
        print("\n🚀 开始完整构建...")
        
        # 检查前提条件
        if not self.check_prerequisites():
            print("\n❌ 前提条件检查失败，构建终止")
            return False
        
        # 构建步骤
        build_steps = [
            ("build_portable_package.py", "构建Portable Python环境"),
            ("create_startup_scripts.py", "创建启动脚本"),
            ("configure_portable_paths.py", "配置便携环境路径"),
            ("create_nsis_installer.py", "创建NSIS安装脚本"),
            ("create_update_system.py", "创建增量更新系统")
        ]
        
        # 执行构建步骤
        for script_name, description in build_steps:
            if not self.run_script(script_name, description):
                print(f"\n❌ 构建失败于步骤: {description}")
                return False
        
        # 创建附加文件
        self.create_readme()
        self.create_version_info()
        
        # 构建完成
        elapsed_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("🎉 完整构建成功！")
        print(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
        print(f"📦 包位置: {self.package_dir}")
        print(f"📊 包大小: {self._get_directory_size(self.package_dir):.2f} MB")
        
        print("\n📋 下一步操作:")
        print("1. 测试便携包: 运行 CompanyPolicySystem/start.bat")
        print("2. 生成安装程序: 使用NSIS编译 build_portable/installer/installer.nsi")
        print("3. 部署测试: 在干净环境中验证功能")
        
        return True
    
    def _get_directory_size(self, path: Path) -> float:
        """获取目录大小（MB）"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size / 1024 / 1024

def main():
    """主函数"""
    try:
        builder = CompletePackageBuilder()
        
        if builder.build():
            print("\n✅ 构建成功完成！")
            sys.exit(0)
        else:
            print("\n❌ 构建失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断构建")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 构建异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
