#!/usr/bin/env python3
"""
创建NSIS安装包脚本
生成专业的Windows安装程序，包含管理员权限、文件权限设置、防火墙例外等功能
"""
import os
from pathlib import Path
import shutil

def create_nsis_installer():
    """创建NSIS安装脚本"""
    project_root = Path(__file__).parent
    build_dir = project_root / "build_portable"
    package_dir = build_dir / "CompanyPolicySystem"
    installer_dir = build_dir / "installer"
    
    print("📦 创建NSIS安装包脚本...")
    
    # 创建安装程序目录
    installer_dir.mkdir(parents=True, exist_ok=True)
    
    # NSIS安装脚本内容
    nsis_script = '''# 公司制度查询平台 NSIS 安装脚本
# 实现零配置交付，确保AI功能在用户环境正常运行

!define PRODUCT_NAME "公司制度查询平台"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "公司名称"
!define PRODUCT_WEB_SITE "https://company.com"
!define PRODUCT_DIR_REGKEY "Software\\Microsoft\\Windows\\CurrentVersion\\App Paths\\CompanyPolicySystem.exe"
!define PRODUCT_UNINST_KEY "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

# 包含必要的头文件
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"
!include "WinVer.nsh"

# 安装程序属性
Name "${PRODUCT_NAME}"
OutFile "CompanyPolicySystem_Setup_v${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES64\\${PRODUCT_NAME}"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

# 请求管理员权限
RequestExecutionLevel admin

# 现代UI配置
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\\Contrib\\Graphics\\Icons\\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\\Contrib\\Graphics\\Icons\\modern-uninstall.ico"

# 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\\start.bat"
!define MUI_FINISHPAGE_RUN_TEXT "立即启动公司制度查询平台"
!insertmacro MUI_PAGE_FINISH

# 卸载页面
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

# 语言文件
!insertmacro MUI_LANGUAGE "SimpChinese"

# 版本信息
VIProductVersion "${PRODUCT_VERSION}.0"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "Comments" "公司制度本地查询平台，支持AI问答和文档检索"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "LegalTrademarks" "${PRODUCT_NAME} is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "LegalCopyright" "© ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileDescription" "${PRODUCT_NAME} 安装程序"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileVersion" "${PRODUCT_VERSION}"

# 安装类型
InstType "完整安装"
InstType "最小安装"

# 主程序组件
Section "主程序文件" SEC01
  SectionIn RO 1 2
  
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  # 复制所有文件
  File /r "CompanyPolicySystem\\*.*"
  
  # 创建必要目录并设置权限
  CreateDirectory "$INSTDIR\\data"
  CreateDirectory "$INSTDIR\\data\\chroma_db"
  CreateDirectory "$INSTDIR\\data\\whoosh_index"
  CreateDirectory "$INSTDIR\\data\\preview_pdfs"
  CreateDirectory "$INSTDIR\\data\\sessions"
  CreateDirectory "$INSTDIR\\logs"
  
  # 设置数据目录权限（允许当前用户完全控制）
  AccessControl::GrantOnFile "$INSTDIR\\data" "(BU)" "FullAccess"
  AccessControl::GrantOnFile "$INSTDIR\\logs" "(BU)" "FullAccess"
  
  # 注册表项
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\\start.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\\start.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

# 桌面快捷方式
Section "桌面快捷方式" SEC02
  SectionIn 1
  CreateShortCut "$DESKTOP\\${PRODUCT_NAME}.lnk" "$INSTDIR\\start.bat" "" "$INSTDIR\\start.bat" 0
SectionEnd

# 开始菜单快捷方式
Section "开始菜单快捷方式" SEC03
  SectionIn 1 2
  CreateDirectory "$SMPROGRAMS\\${PRODUCT_NAME}"
  CreateShortCut "$SMPROGRAMS\\${PRODUCT_NAME}\\${PRODUCT_NAME}.lnk" "$INSTDIR\\start.bat" "" "$INSTDIR\\start.bat" 0
  CreateShortCut "$SMPROGRAMS\\${PRODUCT_NAME}\\环境检查.lnk" "$INSTDIR\\check_environment.bat" "" "$INSTDIR\\check_environment.bat" 0
  CreateShortCut "$SMPROGRAMS\\${PRODUCT_NAME}\\调试模式.lnk" "$INSTDIR\\start_debug.bat" "" "$INSTDIR\\start_debug.bat" 0
  CreateShortCut "$SMPROGRAMS\\${PRODUCT_NAME}\\卸载.lnk" "$INSTDIR\\uninst.exe"
SectionEnd

# Visual C++ 运行库
Section "Visual C++ 运行库" SEC04
  SectionIn 1
  
  # 检查是否已安装 VC++ 2019+ 运行库
  ReadRegStr $0 HKLM "SOFTWARE\\Microsoft\\VisualStudio\\14.0\\VC\\Runtimes\\x64" "Version"
  ${If} $0 == ""
    MessageBox MB_YESNO "检测到系统缺少Visual C++ 2019运行库，这是AI功能正常运行的必要组件。是否现在安装？" IDNO skip_vcredist
    
    # 这里可以添加VC++运行库的安装
    MessageBox MB_OK "请手动下载并安装Visual C++ 2019 x64运行库：$\\nhttps://aka.ms/vs/16/release/vc_redist.x64.exe"
    
    skip_vcredist:
  ${EndIf}
SectionEnd

# 防火墙例外
Section "防火墙例外" SEC05
  SectionIn 1
  
  # 添加防火墙例外（如果程序需要网络访问）
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="${PRODUCT_NAME}" dir=in action=allow program="$INSTDIR\\venv\\Scripts\\python.exe" enable=yes'
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="${PRODUCT_NAME}" dir=out action=allow program="$INSTDIR\\venv\\Scripts\\python.exe" enable=yes'
SectionEnd

# 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "安装主程序文件，包括AI模型、向量数据库和所有必要组件"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "在桌面创建快捷方式"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "在开始菜单创建程序组和快捷方式"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "安装Visual C++运行库（AI功能必需）"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC05} "添加防火墙例外规则"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

# 安装前检查
Function .onInit
  # 检查Windows版本
  ${IfNot} ${AtLeastWin7}
    MessageBox MB_OK "本程序需要Windows 7或更高版本系统"
    Abort
  ${EndIf}
  
  # 检查管理员权限
  UserInfo::GetAccountType
  pop $0
  ${If} $0 != "admin"
    MessageBox MB_OK "安装程序需要管理员权限，请右键选择'以管理员身份运行'"
    Abort
  ${EndIf}
  
  # 检查系统内存
  ${GetTotalPhysicalMemory} $0 $1
  IntOp $0 $0 / 1024  # 转换为MB
  IntOp $0 $0 / 1024  # 转换为GB
  ${If} $0 < 4
    MessageBox MB_YESNO "检测到系统内存少于4GB，AI功能可能运行缓慢。是否继续安装？" IDNO abort_install
  ${EndIf}
  
  Goto continue_install
  abort_install:
    Abort
  continue_install:
FunctionEnd

# 安装完成后
Function .onInstSuccess
  # 创建卸载程序
  WriteUninstaller "$INSTDIR\\uninst.exe"
  
  # 显示安装完成信息
  MessageBox MB_OK "安装完成！$\\n$\\n重要提示：$\\n1. 首次启动可能需要较长时间来初始化AI模型$\\n2. 请确保网络连接正常以下载必要的模型文件$\\n3. 如遇问题请运行'环境检查'工具"
FunctionEnd

# 卸载程序
Section Uninstall
  # 停止可能运行的程序
  nsExec::ExecToLog 'taskkill /f /im python.exe'
  
  # 删除防火墙规则
  nsExec::ExecToLog 'netsh advfirewall firewall delete rule name="${PRODUCT_NAME}"'
  
  # 删除文件
  RMDir /r "$INSTDIR"
  
  # 删除快捷方式
  Delete "$DESKTOP\\${PRODUCT_NAME}.lnk"
  RMDir /r "$SMPROGRAMS\\${PRODUCT_NAME}"
  
  # 删除注册表项
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  
  SetAutoClose true
SectionEnd'''
    
    # 写入NSIS脚本
    nsis_script_path = installer_dir / "installer.nsi"
    with open(nsis_script_path, 'w', encoding='utf-8') as f:
        f.write(nsis_script)
    
    print("✅ NSIS安装脚本已创建")
    
    # 创建许可证文件
    license_content = '''公司制度查询平台 软件许可协议

版权所有 (C) 2024 公司名称

本软件按"现状"提供，不提供任何明示或暗示的担保。

使用条款：
1. 本软件仅供内部使用，不得用于商业目的
2. 禁止逆向工程、反编译或反汇编
3. 禁止未经授权的复制和分发
4. 使用本软件即表示同意本协议条款

技术支持：
- 如遇技术问题，请联系IT部门
- 软件更新将通过内部渠道发布

免责声明：
本软件提供的AI问答功能仅供参考，具体制度执行请以正式文件为准。
'''
    
    license_path = installer_dir / "LICENSE.txt"
    with open(license_path, 'w', encoding='utf-8') as f:
        f.write(license_content)
    
    print("✅ 许可证文件已创建")
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("📦 创建NSIS安装包")
    print("=" * 60)
    
    if create_nsis_installer():
        print("\\n✅ NSIS安装脚本创建成功！")
        print("💡 下一步：")
        print("   1. 确保已安装NSIS (https://nsis.sourceforge.io/)")
        print("   2. 运行构建脚本生成便携包")
        print("   3. 使用NSIS编译安装程序")
    else:
        print("\\n❌ 创建失败！")

if __name__ == "__main__":
    main()
