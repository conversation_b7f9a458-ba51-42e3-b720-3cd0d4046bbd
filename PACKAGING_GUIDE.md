# 公司制度查询平台 - 打包部署指南

## 概述

本指南详细说明如何将公司制度查询平台打包为零配置交付包，确保AI问答功能在用户环境中正常运行。

## 核心目标

✅ **AI问答功能正常运行** - 确保大语言模型和嵌入模型在便携环境中可用  
✅ **制度检索功能完整可用** - 向量数据库和全文索引正常工作  
✅ **向量数据库开箱即用** - 使用绝对路径，自动创建目录和设置权限  
✅ **文档预览精准定位** - PDF预览和页面跳转功能完整  

## 系统要求

### 构建环境
- Windows 10/11 (64位)
- Python 3.8+ 
- 内存: 8GB以上
- 硬盘: 10GB可用空间
- 网络连接（下载依赖和模型）

### 目标环境
- Windows 7/8/10/11 (64位)
- 内存: 4GB以上 (推荐8GB)
- 硬盘: 2GB可用空间
- Visual C++ 2019运行库

## 快速开始

### 一键构建和测试
```bash
# 完整构建和测试
python build_and_test.py

# 仅构建
python build_and_test.py --build-only

# 仅测试
python build_and_test.py --test-only
```

### 分步骤构建
```bash
# 1. 构建Portable Python环境
python build_portable_package.py

# 2. 创建启动脚本
python create_startup_scripts.py

# 3. 配置便携路径
python configure_portable_paths.py

# 4. 创建NSIS安装脚本
python create_nsis_installer.py

# 5. 创建更新系统
python create_update_system.py

# 6. 验证测试
python test_package.py
```

## 构建产物

### 便携包结构
```
build_portable/CompanyPolicySystem/
├── main.py                    # 主程序
├── config.yaml               # 配置文件
├── start.bat                 # 启动脚本
├── start_debug.bat           # 调试启动
├── check_environment.bat     # 环境检查
├── UpdateTool.py            # 更新工具
├── UpdateTool.bat           # 更新工具启动
├── README.md                # 使用说明
├── version.json             # 版本信息
├── venv/                    # Python虚拟环境
├── src/                     # 源代码
├── data/                    # 数据目录
│   ├── chroma_db/          # 向量数据库
│   ├── whoosh_index/       # 全文索引
│   ├── preview_pdfs/       # PDF预览
│   └── sessions/           # 会话数据
├── models/                  # AI模型
├── docs/                    # 文档目录
├── static/                  # 静态资源
└── logs/                    # 日志目录
```

### 安装程序
```
build_portable/installer/
├── installer.nsi           # NSIS安装脚本
└── LICENSE.txt            # 许可证文件
```

### 更新工具
```
build_portable/update_tools/
└── generate_update.py     # 更新包生成器
```

## 部署方式

### 方式一：便携包部署（推荐）
1. 将 `CompanyPolicySystem` 目录复制到目标机器
2. 双击 `start.bat` 启动程序
3. 首次启动会初始化AI模型

### 方式二：安装程序部署
1. 使用NSIS编译 `installer.nsi` 生成安装程序
2. 在目标机器运行安装程序
3. 从开始菜单启动程序

## 关键技术实现

### 1. Portable Python环境
- 使用Python嵌入式版本
- 创建完整虚拟环境
- 安装所有依赖包
- 配置环境变量

### 2. 绝对路径配置
```python
# 自动检测应用根目录
if getattr(sys, 'frozen', False):
    # 便携Python环境
    self.base_dir = Path(sys.executable).parent.parent
else:
    # 开发环境
    self.base_dir = Path(__file__).parent.parent.parent

# 支持环境变量覆盖
if 'APP_PATH' in os.environ:
    self.base_dir = Path(os.environ['APP_PATH'])
```

### 3. 向量数据库配置
```python
# 确保使用绝对路径
persist_dir = self.config.database.chroma_persist_dir
if not os.path.isabs(persist_dir):
    persist_dir = os.path.join(self.config.base_dir, persist_dir)
self.persist_directory = persist_dir

# 确保目录存在
os.makedirs(self.persist_directory, exist_ok=True)
```

### 4. 启动脚本优化
```batch
:: 设置环境变量
set "PYTHONPATH=!APP_PATH!"
set "VECTOR_DB_PATH=!APP_PATH!\\data\\chroma_db"
set "MODELS_PATH=!APP_PATH!\\models"

:: 性能优化
set "OMP_NUM_THREADS=4"
set "MKL_NUM_THREADS=4"
set "TORCH_NUM_THREADS=4"

:: 激活虚拟环境
call "!APP_PATH!\\venv\\Scripts\\activate.bat"
```

## 增量更新机制

### 管理员操作
1. 运行更新包生成器：
   ```bash
   python build_portable/update_tools/generate_update.py
   ```
2. 生成的更新包发送给用户

### 用户操作
1. 双击 `UpdateTool.bat`
2. 选择更新包文件
3. 自动备份和应用更新
4. 重启程序

## 故障排除

### 构建失败
1. 检查Python版本和依赖
2. 确认网络连接正常
3. 检查磁盘空间充足
4. 查看详细错误日志

### AI功能不可用
1. 检查模型文件是否完整
2. 确认内存是否充足
3. 验证torch等依赖是否正确安装
4. 查看应用日志文件

### 路径问题
1. 运行 `check_environment.bat` 检查环境
2. 确认 `APP_PATH` 环境变量设置
3. 检查数据目录权限

## 性能优化

### 内存优化
- 限制AI模型内存使用
- 配置合适的线程数
- 启用模型量化

### 启动优化
- 预编译Python字节码
- 缓存模型加载
- 延迟初始化非关键组件

### 磁盘优化
- 压缩模型文件
- 清理临时文件
- 优化向量数据库存储

## 测试验证

### 自动化测试
```bash
python test_package.py
```

### 手动测试清单
- [ ] 程序正常启动
- [ ] AI问答功能可用
- [ ] 文档检索返回结果
- [ ] PDF预览正常显示
- [ ] 更新工具可用
- [ ] 环境检查通过

## 注意事项

1. **首次启动时间较长** - AI模型初始化需要时间
2. **网络依赖** - 首次运行可能需要下载模型
3. **内存要求** - AI功能需要足够内存
4. **权限设置** - 确保数据目录可写
5. **防病毒软件** - 可能误报Python可执行文件

## 技术支持

如遇问题，请提供：
- 错误截图
- 日志文件 (`logs/app.log`)
- 系统环境信息
- 测试报告 (`build_portable/test_report.json`)

---
© 2024 公司名称 - 内部技术文档
